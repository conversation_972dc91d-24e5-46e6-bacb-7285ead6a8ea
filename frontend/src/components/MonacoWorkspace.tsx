import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  Alert,
  CircularProgress,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  InsertDriveFile as FileIcon,
  Save as SaveIcon,
  Undo as UndoIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { Editor } from '@monaco-editor/react';

interface FileNode {
  name: string;
  path: string;
  type: 'file' | 'folder';
  content?: string;
  language?: string;
  children?: FileNode[];
  isOpen?: boolean;
  isModified?: boolean;
  originalContent?: string;
}

interface OpenTab {
  path: string;
  name: string;
  content: string;
  language: string;
  isModified: boolean;
  originalContent: string;
}

interface MonacoWorkspaceProps {
  interviewUuid: string;
  projectStructure?: any;
  onFileChange?: (path: string, content: string) => void;
  onSave?: (files: { [path: string]: string }) => void;
  onRevert?: (path: string) => void;
  height?: string | number;
  minHeight?: string | number;
}

const MonacoWorkspace: React.FC<MonacoWorkspaceProps> = ({
  interviewUuid,
  projectStructure,
  onFileChange,
  onSave,
  onRevert,
  height = '100%',
  minHeight = '600px'
}) => {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [openTabs, setOpenTabs] = useState<OpenTab[]>([]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<any>(null);

  // Language mapping for Monaco Editor
  const getLanguageFromExtension = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'sql': 'sql',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'txt': 'plaintext'
    };
    return languageMap[ext || ''] || 'plaintext';
  };

  // Convert project structure to file tree
  const buildFileTree = useCallback((structure: any): FileNode[] => {
    if (!structure) return [];

    const buildNode = (name: string, content: any, parentPath: string = ''): FileNode => {
      const currentPath = parentPath ? `${parentPath}/${name}` : name;

      // Handle new API format with type/content/children structure
      if (typeof content === 'object' && content !== null && content.type) {
        if (content.type === 'file') {
          return {
            name,
            path: currentPath,
            type: 'file',
            content: content.content || '',
            originalContent: content.content || '',
            language: content.language || getLanguageFromExtension(name),
            isModified: false
          };
        } else if (content.type === 'folder' && content.children) {
          const children = Object.entries(content.children).map(([childName, childContent]) =>
            buildNode(childName, childContent, currentPath)
          );

          return {
            name,
            path: currentPath,
            type: 'folder',
            children,
            isOpen: true // Start with folders open
          };
        }
      }
      // Handle legacy format (simple string content or object)
      else if (typeof content === 'string') {
        // It's a file
        return {
          name,
          path: currentPath,
          type: 'file',
          content,
          originalContent: content,
          language: getLanguageFromExtension(name),
          isModified: false
        };
      } else if (typeof content === 'object' && content !== null) {
        // It's a folder (legacy format)
        const children = Object.entries(content).map(([childName, childContent]) =>
          buildNode(childName, childContent, currentPath)
        );

        return {
          name,
          path: currentPath,
          type: 'folder',
          children,
          isOpen: true // Start with folders open
        };
      }

      return {
        name,
        path: currentPath,
        type: 'file',
        content: '',
        originalContent: '',
        language: 'plaintext',
        isModified: false
      };
    };

    return Object.entries(structure).map(([name, content]) =>
      buildNode(name, content)
    );
  }, []);

  // Initialize file tree when project structure changes or interviewUuid changes
  useEffect(() => {
    if (projectStructure && interviewUuid) {
      console.log('🌳 Building file tree from project structure for interview:', interviewUuid);
      const tree = buildFileTree(projectStructure);
      setFileTree(tree);

      // Auto-open the first file if available
      const firstFile = findFirstFile(tree);
      if (firstFile && openTabs.length === 0) {
        openFile(firstFile);
      }
    }
  }, [projectStructure, interviewUuid, buildFileTree]);

  // Clear workspace when interviewUuid changes to ensure isolation
  useEffect(() => {
    console.log('🔄 Interview UUID changed, clearing workspace state for isolation:', interviewUuid);
    setFileTree([]);
    setOpenTabs([]);
    setActiveTab('');
    setError('');
    setHasUnsavedChanges(false);
  }, [interviewUuid]);

  // Find first file in tree for auto-opening
  const findFirstFile = (nodes: FileNode[]): FileNode | null => {
    for (const node of nodes) {
      if (node.type === 'file') {
        return node;
      } else if (node.children) {
        const found = findFirstFile(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  // Toggle folder open/close
  const toggleFolder = (path: string) => {
    const updateNode = (nodes: FileNode[]): FileNode[] => {
      return nodes.map(node => {
        if (node.path === path && node.type === 'folder') {
          return { ...node, isOpen: !node.isOpen };
        } else if (node.children) {
          return { ...node, children: updateNode(node.children) };
        }
        return node;
      });
    };
    
    setFileTree(updateNode(fileTree));
  };

  // Open file in new tab or switch to existing tab
  const openFile = (fileNode: FileNode) => {
    if (fileNode.type !== 'file') return;

    const existingTabIndex = openTabs.findIndex(tab => tab.path === fileNode.path);
    
    if (existingTabIndex >= 0) {
      // Switch to existing tab
      setActiveTab(fileNode.path);
    } else {
      // Open new tab
      const newTab: OpenTab = {
        path: fileNode.path,
        name: fileNode.name,
        content: fileNode.content || '',
        language: fileNode.language || 'plaintext',
        isModified: false,
        originalContent: fileNode.content || ''
      };
      
      setOpenTabs(prev => [...prev, newTab]);
      setActiveTab(fileNode.path);
    }
  };

  // Close tab
  const closeTab = (path: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    
    const tabIndex = openTabs.findIndex(tab => tab.path === path);
    if (tabIndex === -1) return;

    const newTabs = openTabs.filter(tab => tab.path !== path);
    setOpenTabs(newTabs);

    // Switch to another tab if the closed tab was active
    if (activeTab === path) {
      if (newTabs.length > 0) {
        const newActiveIndex = Math.min(tabIndex, newTabs.length - 1);
        setActiveTab(newTabs[newActiveIndex].path);
      } else {
        setActiveTab('');
      }
    }
  };

  // Handle editor content change
  const handleEditorChange = (value: string | undefined, path: string) => {
    if (value === undefined) return;

    const updatedTabs = openTabs.map(tab => {
      if (tab.path === path) {
        const isModified = value !== tab.originalContent;
        return { ...tab, content: value, isModified };
      }
      return tab;
    });

    setOpenTabs(updatedTabs);
    
    // Update hasUnsavedChanges state
    const hasChanges = updatedTabs.some(tab => tab.isModified);
    setHasUnsavedChanges(hasChanges);

    // Notify parent component
    onFileChange?.(path, value);
  };

  // Save all modified files
  const saveAllFiles = () => {
    const modifiedFiles: { [path: string]: string } = {};
    
    openTabs.forEach(tab => {
      if (tab.isModified) {
        modifiedFiles[tab.path] = tab.content;
      }
    });

    if (Object.keys(modifiedFiles).length > 0) {
      onSave?.(modifiedFiles);
      
      // Mark all tabs as saved
      const savedTabs = openTabs.map(tab => ({
        ...tab,
        isModified: false,
        originalContent: tab.content
      }));
      
      setOpenTabs(savedTabs);
      setHasUnsavedChanges(false);
    }
  };

  // Revert file to original content
  const revertFile = (path: string) => {
    const updatedTabs = openTabs.map(tab => {
      if (tab.path === path) {
        return {
          ...tab,
          content: tab.originalContent,
          isModified: false
        };
      }
      return tab;
    });

    setOpenTabs(updatedTabs);
    
    // Update hasUnsavedChanges state
    const hasChanges = updatedTabs.some(tab => tab.isModified);
    setHasUnsavedChanges(hasChanges);

    onRevert?.(path);
  };

  return (
    <Box
      sx={{
        height: typeof height === 'string' ? height : `${height}px`,
        minHeight: typeof minHeight === 'string' ? minHeight : `${minHeight}px`,
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#1e1e1e',
        color: '#d4d4d4'
      }}
    >
      {/* Toolbar */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 1,
          borderBottom: 1,
          borderColor: '#333',
          bgcolor: '#2d2d30'
        }}
      >
        <Typography variant="h6" sx={{ color: '#d4d4d4' }}>
          Monaco Workspace
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Save All Files">
            <span>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                onClick={saveAllFiles}
                disabled={!hasUnsavedChanges}
                sx={{
                  bgcolor: hasUnsavedChanges ? '#007acc' : '#3c3c3c',
                  '&:hover': {
                    bgcolor: hasUnsavedChanges ? '#005a9e' : '#4a4a4a'
                  }
                }}
              >
                Save
              </Button>
            </span>
          </Tooltip>
          
          {activeTab && (
            <Tooltip title="Revert Current File">
              <span>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<UndoIcon />}
                  onClick={() => revertFile(activeTab)}
                  disabled={!openTabs.find(tab => tab.path === activeTab)?.isModified}
                  sx={{
                    borderColor: '#555',
                    color: '#d4d4d4',
                    '&:hover': {
                      borderColor: '#777',
                      bgcolor: '#3c3c3c'
                    }
                  }}
                >
                  Revert
                </Button>
              </span>
            </Tooltip>
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ m: 1 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', flex: 1, minHeight: 0 }}>
        {/* File Tree Sidebar */}
        <Box
          sx={{
            width: 250,
            borderRight: 1,
            borderColor: '#333',
            bgcolor: '#252526',
            overflow: 'auto'
          }}
        >
          <Typography
            variant="subtitle2"
            sx={{
              p: 1,
              bgcolor: '#2d2d30',
              borderBottom: 1,
              borderColor: '#333',
              color: '#cccccc'
            }}
          >
            Explorer
          </Typography>
          
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : (
            <FileTreeNode
              nodes={fileTree}
              onFileClick={openFile}
              onFolderToggle={toggleFolder}
            />
          )}
        </Box>

        {/* Editor Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {openTabs.length > 0 ? (
            <>
              {/* Tabs */}
              <Box
                sx={{
                  borderBottom: 1,
                  borderColor: '#333',
                  bgcolor: '#2d2d30',
                  minHeight: 48
                }}
              >
                <Tabs
                  value={activeTab}
                  onChange={(_, newValue) => setActiveTab(newValue)}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    '& .MuiTab-root': {
                      color: '#cccccc',
                      minHeight: 48,
                      textTransform: 'none',
                      fontSize: '0.875rem'
                    },
                    '& .Mui-selected': {
                      color: '#ffffff',
                      bgcolor: '#1e1e1e'
                    },
                    '& .MuiTabs-indicator': {
                      backgroundColor: '#007acc'
                    }
                  }}
                >
                  {openTabs.map((tab) => (
                    <Tab
                      key={tab.path}
                      value={tab.path}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <span>
                            {tab.name}
                            {tab.isModified && (
                              <span style={{ color: '#f0f0f0', marginLeft: 4 }}>●</span>
                            )}
                          </span>
                          <IconButton
                            size="small"
                            onClick={(e) => closeTab(tab.path, e)}
                            sx={{
                              color: '#cccccc',
                              padding: '2px',
                              '&:hover': {
                                bgcolor: '#3c3c3c'
                              }
                            }}
                          >
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      }
                    />
                  ))}
                </Tabs>
              </Box>

              {/* Editor */}
              <Box sx={{ flex: 1 }}>
                {openTabs.map((tab) => (
                  <Box
                    key={tab.path}
                    sx={{
                      height: '100%',
                      display: activeTab === tab.path ? 'block' : 'none'
                    }}
                  >
                    <Editor
                      height="100%"
                      language={tab.language}
                      value={tab.content}
                      onChange={(value) => handleEditorChange(value, tab.path)}
                      theme="vs-dark"
                      onMount={(editor, monaco) => {
                        editorRef.current = editor;
                        monacoRef.current = monaco;
                      }}
                      options={{
                        fontSize: 14,
                        lineHeight: 20,
                        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                        minimap: { enabled: true },
                        scrollBeyondLastLine: false,
                        automaticLayout: true,
                        tabSize: 2,
                        insertSpaces: true,
                        wordWrap: 'on',
                        lineNumbers: 'on',
                        glyphMargin: true,
                        folding: true,
                        renderWhitespace: 'selection'
                      }}
                    />
                  </Box>
                ))}
              </Box>
            </>
          ) : (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#888'
              }}
            >
              <Typography variant="h6">
                No files open. Select a file from the explorer to start editing.
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

// File Tree Node Component
interface FileTreeNodeProps {
  nodes: FileNode[];
  onFileClick: (file: FileNode) => void;
  onFolderToggle: (path: string) => void;
  level?: number;
}

const FileTreeNode: React.FC<FileTreeNodeProps> = ({
  nodes,
  onFileClick,
  onFolderToggle,
  level = 0
}) => {
  return (
    <List dense sx={{ py: 0 }}>
      {nodes.map((node) => (
        <React.Fragment key={node.path}>
          <ListItem
            component="div"
            onClick={() => {
              if (node.type === 'file') {
                onFileClick(node);
              } else {
                onFolderToggle(node.path);
              }
            }}
            sx={{
              pl: 1 + level * 2,
              py: 0.25,
              minHeight: 28,
              cursor: 'pointer',
              '&:hover': {
                bgcolor: '#2a2d2e'
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 24, color: '#cccccc' }}>
              {node.type === 'folder' ? (
                node.isOpen ? <FolderOpenIcon fontSize="small" /> : <FolderIcon fontSize="small" />
              ) : (
                <FileIcon fontSize="small" />
              )}
            </ListItemIcon>
            <ListItemText
              primary={node.name}
              slotProps={{
                primary: {
                  fontSize: '0.875rem',
                  color: '#cccccc'
                }
              }}
            />
          </ListItem>
          
          {node.type === 'folder' && node.children && node.isOpen && (
            <Collapse in={node.isOpen} timeout="auto" unmountOnExit>
              <FileTreeNode
                nodes={node.children}
                onFileClick={onFileClick}
                onFolderToggle={onFolderToggle}
                level={level + 1}
              />
            </Collapse>
          )}
        </React.Fragment>
      ))}
    </List>
  );
};

export default MonacoWorkspace;
