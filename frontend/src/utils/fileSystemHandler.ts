/**
 * File System Handler for Monaco Editor Workspace
 * Handles saving and loading files to/from the local workspace directory
 */

interface FileSystemResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

export class FileSystemHandler {
  private static readonly WORKSPACE_BASE_PATH = '/Users/<USER>/Desktop/Mergen-AI/mergen-code';
  
  /**
   * Save files to the local workspace directory
   */
  static async saveFiles(
    interviewUuid: string, 
    files: { [path: string]: string }
  ): Promise<FileSystemResponse> {
    try {
      console.log('💾 Saving files to local filesystem:', Object.keys(files));
      
      // Create workspace directory for this interview if it doesn't exist
      const workspaceDir = `${this.WORKSPACE_BASE_PATH}/workspace/${interviewUuid}`;
      
      // For now, we'll use the backend API to handle file operations
      // since frontend can't directly write to filesystem
      const response = await fetch('/api/filesystem/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewUuid,
          files,
          workspaceDir
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Files saved successfully to:', workspaceDir);
        return {
          success: true,
          message: `Saved ${Object.keys(files).length} files to workspace`,
          data: result.data
        };
      } else {
        throw new Error(result.error || 'Failed to save files');
      }

    } catch (error: any) {
      console.error('❌ Error saving files:', error);
      return {
        success: false,
        error: error.message || 'Failed to save files to filesystem'
      };
    }
  }

  /**
   * Load files from the local workspace directory
   */
  static async loadFiles(interviewUuid: string): Promise<FileSystemResponse> {
    try {
      console.log('📂 Loading files from local filesystem for:', interviewUuid);
      
      const response = await fetch(`/api/filesystem/load/${interviewUuid}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Files loaded successfully:', Object.keys(result.data || {}));
        return {
          success: true,
          message: 'Files loaded from workspace',
          data: result.data
        };
      } else {
        throw new Error(result.error || 'Failed to load files');
      }

    } catch (error: any) {
      console.error('❌ Error loading files:', error);
      return {
        success: false,
        error: error.message || 'Failed to load files from filesystem'
      };
    }
  }

  /**
   * Revert a file to its original content
   */
  static async revertFile(
    interviewUuid: string, 
    filePath: string, 
    originalContent: string
  ): Promise<FileSystemResponse> {
    try {
      console.log('🔄 Reverting file:', filePath);
      
      // Save the original content back to the file
      return await this.saveFiles(interviewUuid, { [filePath]: originalContent });

    } catch (error: any) {
      console.error('❌ Error reverting file:', error);
      return {
        success: false,
        error: error.message || 'Failed to revert file'
      };
    }
  }

  /**
   * Check if workspace directory exists
   */
  static async checkWorkspaceExists(interviewUuid: string): Promise<FileSystemResponse> {
    try {
      const response = await fetch(`/api/filesystem/exists/${interviewUuid}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (error: any) {
      console.error('❌ Error checking workspace:', error);
      return {
        success: false,
        error: error.message || 'Failed to check workspace'
      };
    }
  }

  /**
   * Create workspace directory structure from project structure
   */
  static async createWorkspaceFromStructure(
    interviewUuid: string, 
    projectStructure: any
  ): Promise<FileSystemResponse> {
    try {
      console.log('🏗️ Creating workspace from project structure');
      
      const files = this.flattenProjectStructure(projectStructure);
      return await this.saveFiles(interviewUuid, files);

    } catch (error: any) {
      console.error('❌ Error creating workspace:', error);
      return {
        success: false,
        error: error.message || 'Failed to create workspace'
      };
    }
  }

  /**
   * Flatten project structure into file paths and content
   */
  private static flattenProjectStructure(
    structure: any, 
    basePath: string = ''
  ): { [path: string]: string } {
    const files: { [path: string]: string } = {};

    Object.entries(structure).forEach(([name, content]) => {
      const currentPath = basePath ? `${basePath}/${name}` : name;
      
      if (typeof content === 'string') {
        // It's a file
        files[currentPath] = content;
      } else if (typeof content === 'object' && content !== null) {
        // It's a directory - recurse
        const subFiles = this.flattenProjectStructure(content, currentPath);
        Object.assign(files, subFiles);
      }
    });

    return files;
  }

  /**
   * Get workspace path for an interview
   */
  static getWorkspacePath(interviewUuid: string): string {
    return `${this.WORKSPACE_BASE_PATH}/workspace/${interviewUuid}`;
  }

  /**
   * Validate file path to prevent directory traversal attacks
   */
  private static validateFilePath(filePath: string): boolean {
    // Prevent directory traversal
    if (filePath.includes('..') || filePath.startsWith('/')) {
      return false;
    }
    
    // Only allow reasonable file extensions
    const allowedExtensions = [
      '.js', '.jsx', '.ts', '.tsx', '.json', '.html', '.css', '.scss',
      '.py', '.java', '.cpp', '.c', '.php', '.rb', '.go', '.rs',
      '.sql', '.xml', '.yaml', '.yml', '.md', '.txt', '.env'
    ];
    
    const extension = filePath.substring(filePath.lastIndexOf('.'));
    return allowedExtensions.includes(extension) || !extension;
  }

  /**
   * Sanitize file content to prevent malicious code injection
   */
  private static sanitizeContent(content: string): string {
    // Basic sanitization - remove null bytes and control characters
    return content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }
}

export default FileSystemHandler;
