import llmResponseData from '../../doc/llm_response.json';
import llmReplyData from '../../doc/llm_reply.json';

interface ExtractedContent {
  description?: string;
  codeBlocks: Array<{
    language: string;
    filename?: string;
    content: string;
  }>;
  projectStructure?: any;
  deploymentInstructions?: string;
  additionalSections?: { [key: string]: string };
}

interface MockApiResponse {
  success: boolean;
  content: string;
  extractedContent?: ExtractedContent;
  buildResultUuid?: string;
  metadata: any;
}

export class MockApiHandler {
  /**
   * Check if we should use mock responses (no API key configured)
   */
  static shouldUseMockResponse(): boolean {
    // Check if API key is configured in environment or localStorage
    const hasApiKey = process.env.REACT_APP_ANTHROPIC_API_KEY || 
                     localStorage.getItem('anthropic_api_key');
    return !hasApiKey;
  }

  /**
   * Get mock response for initial project builds
   */
  static getMockBuildResponse(interviewUuid: string): MockApiResponse {
    const content = llmResponseData.content[0].text;
    const extractedContent = this.extractContentFromMockResponse(content);
    
    return {
      success: true,
      content,
      extractedContent,
      buildResultUuid: `mock-build-${interviewUuid}-${Date.now()}`,
      metadata: {
        model: llmResponseData.model,
        usage: llmResponseData.usage,
        isMock: true
      }
    };
  }

  /**
   * Get mock response for chat messages
   */
  static getMockChatResponse(userMessage: string): MockApiResponse {
    const content = llmReplyData.content[0].text;
    const extractedContent = this.extractContentFromMockResponse(content);
    
    return {
      success: true,
      content,
      extractedContent,
      metadata: {
        model: llmReplyData.model,
        usage: llmReplyData.usage,
        isMock: true,
        userMessage
      }
    };
  }

  /**
   * Extract file structure and content from mock response text
   */
  private static extractContentFromMockResponse(content: string): ExtractedContent {
    const extracted: ExtractedContent = {
      description: '',
      codeBlocks: [],
      projectStructure: null,
      deploymentInstructions: '',
      additionalSections: {}
    };

    try {
      // Extract description (text before first code block or structure)
      const descriptionMatch = content.match(/^(.*?)(?=```|##\s*Project\s*Structure)/s);
      if (descriptionMatch) {
        extracted.description = descriptionMatch[1].trim();
      }

      // Extract deployment instructions
      const deploymentMatch = content.match(/##\s*Deployment\s*Instructions?\s*\n(.*?)(?=\n##|\n```|$)/s);
      if (deploymentMatch) {
        extracted.deploymentInstructions = deploymentMatch[1].trim();
      }

      // Extract code blocks
      const codeBlockRegex = /```(\w+)?\s*(?:\n### (.+?)\n)?(.*?)```/gs;
      let match;
      
      while ((match = codeBlockRegex.exec(content)) !== null) {
        const [, language = 'text', filename, codeContent] = match;
        
        extracted.codeBlocks.push({
          language: language.toLowerCase(),
          filename: filename?.trim(),
          content: codeContent.trim()
        });
      }

      // Try to build project structure from code blocks
      extracted.projectStructure = this.buildProjectStructureFromCodeBlocks(extracted.codeBlocks);

      // Extract additional sections
      const sectionRegex = /##\s*([^#\n]+)\s*\n(.*?)(?=\n##|\n```|$)/gs;
      let sectionMatch;
      
      while ((sectionMatch = sectionRegex.exec(content)) !== null) {
        const [, title, sectionContent] = sectionMatch;
        const cleanTitle = title.trim().toLowerCase();
        
        if (!['project structure', 'deployment instructions'].includes(cleanTitle)) {
          extracted.additionalSections = extracted.additionalSections || {};
          extracted.additionalSections[cleanTitle] = sectionContent.trim();
        }
      }

    } catch (error) {
      console.error('Error extracting content from mock response:', error);
    }

    return extracted;
  }

  /**
   * Build project structure from code blocks
   */
  private static buildProjectStructureFromCodeBlocks(codeBlocks: Array<{
    language: string;
    filename?: string;
    content: string;
  }>): any {
    const structure: any = {};

    codeBlocks.forEach(block => {
      if (!block.filename) return;

      const pathParts = block.filename.split('/');
      let current = structure;

      // Navigate/create the directory structure
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }

      // Set the file content
      const filename = pathParts[pathParts.length - 1];
      current[filename] = block.content;
    });

    return Object.keys(structure).length > 0 ? structure : null;
  }

  /**
   * Update existing project structure with new files from chat response
   */
  static updateProjectStructure(
    existingStructure: any,
    newExtractedContent: ExtractedContent
  ): any {
    if (!newExtractedContent.projectStructure) {
      return existingStructure;
    }

    // Deep merge the structures
    const mergeStructures = (existing: any, newStruct: any): any => {
      const result = { ...existing };

      Object.entries(newStruct).forEach(([key, value]) => {
        if (typeof value === 'string') {
          // It's a file - replace or add
          result[key] = value;
        } else if (typeof value === 'object' && value !== null) {
          // It's a directory - merge recursively
          if (result[key] && typeof result[key] === 'object') {
            result[key] = mergeStructures(result[key], value);
          } else {
            result[key] = value;
          }
        }
      });

      return result;
    };

    return mergeStructures(existingStructure || {}, newExtractedContent.projectStructure);
  }

  /**
   * Extract only the projectStructure content for Sphere Engine uploads
   * (as per user requirements)
   */
  static getProjectStructureForUpload(extractedContent: ExtractedContent): any {
    return extractedContent.projectStructure || {};
  }

  /**
   * Simulate API delay for more realistic mock behavior
   */
  static async simulateApiDelay(minMs: number = 1000, maxMs: number = 3000): Promise<void> {
    const delay = Math.random() * (maxMs - minMs) + minMs;
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Create mock build result for database storage
   */
  static createMockBuildResult(interviewUuid: string, extractedContent: ExtractedContent) {
    return {
      uuid: `mock-build-${interviewUuid}-${Date.now()}`,
      interviewUuid,
      description: extractedContent.description || '',
      codeBlocks: extractedContent.codeBlocks,
      projectStructure: extractedContent.projectStructure,
      deploymentInstructions: extractedContent.deploymentInstructions || '',
      additionalSections: extractedContent.additionalSections || {},
      status: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      chatHistory: [],
      metadata: {
        isMock: true,
        model: 'mock-claude-sonnet-4',
        usage: {
          input_tokens: 100,
          output_tokens: 1000,
          total_tokens: 1100
        }
      }
    };
  }

  /**
   * Create mock chat messages for progressive display
   */
  static createMockChatMessages(extractedContent: ExtractedContent): Array<{
    id: string;
    type: 'ai' | 'user';
    content: string;
    timestamp: Date;
  }> {
    const messages = [];

    if (extractedContent.description) {
      messages.push({
        id: `mock-msg-${Date.now()}-1`,
        type: 'ai' as const,
        content: extractedContent.description,
        timestamp: new Date()
      });
    }

    if (extractedContent.codeBlocks.length > 0) {
      messages.push({
        id: `mock-msg-${Date.now()}-2`,
        type: 'ai' as const,
        content: `I've generated ${extractedContent.codeBlocks.length} files for your project. You can now edit them in the Monaco Editor workspace.`,
        timestamp: new Date()
      });
    }

    if (extractedContent.deploymentInstructions) {
      messages.push({
        id: `mock-msg-${Date.now()}-3`,
        type: 'ai' as const,
        content: `**Deployment Instructions:**\n\n${extractedContent.deploymentInstructions}`,
        timestamp: new Date()
      });
    }

    return messages;
  }

  /**
   * Check if content contains file structure updates
   */
  static hasFileUpdates(extractedContent: ExtractedContent): boolean {
    return (extractedContent.codeBlocks && extractedContent.codeBlocks.length > 0) ||
           (extractedContent.projectStructure && Object.keys(extractedContent.projectStructure).length > 0);
  }
}

export default MockApiHandler;
