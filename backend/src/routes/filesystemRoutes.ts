import express from 'express';
import {
  saveFiles,
  loadFiles,
  checkWorkspaceExists,
  createWorkspaceDirectory
} from '../controllers/filesystemController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// All filesystem routes require authentication
router.use(authenticateToken);

// POST /api/filesystem/save - Save files to workspace directory
router.post('/save', saveFiles);

// GET /api/filesystem/load/:interviewUuid - Load files from workspace directory
router.get('/load/:interviewUuid', loadFiles);

// GET /api/filesystem/exists/:interviewUuid - Check if workspace exists
router.get('/exists/:interviewUuid', checkWorkspaceExists);

// POST /api/filesystem/create - Create workspace directory
router.post('/create', createWorkspaceDirectory);

export default router;
