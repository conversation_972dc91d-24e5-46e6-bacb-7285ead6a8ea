import { Request, Response } from 'express';
import fs from 'fs/promises';
import path from 'path';
import { AuthenticatedRequest } from '../middleware/auth';

const WORKSPACE_BASE_PATH = '/Users/<USER>/Desktop/Mergen-AI/mergen-code';

interface SaveFilesRequest {
  interviewUuid: string;
  files: { [path: string]: string };
  workspaceDir?: string;
}

interface FileSystemResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

/**
 * Save files to the local workspace directory
 */
export const saveFiles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, files, workspaceDir }: SaveFilesRequest = req.body;

    if (!interviewUuid || !files) {
      return res.status(400).json({
        success: false,
        error: 'interviewUuid and files are required'
      });
    }

    const targetDir = workspaceDir || path.join(WORKSPACE_BASE_PATH, 'workspace', interviewUuid);
    
    console.log('💾 Saving files to:', targetDir);
    console.log('📁 Files to save:', Object.keys(files));

    // Ensure workspace directory exists
    await fs.mkdir(targetDir, { recursive: true });

    const savedFiles: string[] = [];
    const errors: string[] = [];

    // Save each file
    for (const [filePath, content] of Object.entries(files)) {
      try {
        // Validate file path
        if (!validateFilePath(filePath)) {
          errors.push(`Invalid file path: ${filePath}`);
          continue;
        }

        const fullPath = path.join(targetDir, filePath);
        const fileDir = path.dirname(fullPath);

        // Ensure directory exists
        await fs.mkdir(fileDir, { recursive: true });

        // Sanitize and save content
        const sanitizedContent = sanitizeContent(content);
        await fs.writeFile(fullPath, sanitizedContent, 'utf8');
        
        savedFiles.push(filePath);
        console.log('✅ Saved file:', filePath);

      } catch (error: any) {
        console.error(`❌ Error saving file ${filePath}:`, error);
        errors.push(`Failed to save ${filePath}: ${error.message}`);
      }
    }

    const response: FileSystemResponse = {
      success: savedFiles.length > 0,
      message: `Saved ${savedFiles.length} files${errors.length > 0 ? ` (${errors.length} errors)` : ''}`,
      data: {
        savedFiles,
        errors,
        workspaceDir: targetDir
      }
    };

    if (errors.length > 0) {
      response.error = errors.join('; ');
    }

    res.json(response);

  } catch (error: any) {
    console.error('❌ Error in saveFiles:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to save files'
    });
  }
};

/**
 * Load files from the local workspace directory
 */
export const loadFiles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        error: 'interviewUuid is required'
      });
    }

    const workspaceDir = path.join(WORKSPACE_BASE_PATH, 'workspace', interviewUuid);
    
    console.log('📂 Loading files from:', workspaceDir);

    // Check if workspace exists
    try {
      await fs.access(workspaceDir);
    } catch {
      return res.json({
        success: true,
        message: 'Workspace does not exist yet',
        data: {}
      });
    }

    const files = await loadFilesRecursively(workspaceDir, workspaceDir);

    console.log('✅ Loaded files:', Object.keys(files));

    res.json({
      success: true,
      message: `Loaded ${Object.keys(files).length} files`,
      data: files
    });

  } catch (error: any) {
    console.error('❌ Error in loadFiles:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to load files'
    });
  }
};

/**
 * Check if workspace directory exists
 */
export const checkWorkspaceExists = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        error: 'interviewUuid is required'
      });
    }

    const workspaceDir = path.join(WORKSPACE_BASE_PATH, 'workspace', interviewUuid);
    
    try {
      const stats = await fs.stat(workspaceDir);
      res.json({
        success: true,
        data: {
          exists: stats.isDirectory(),
          path: workspaceDir
        }
      });
    } catch {
      res.json({
        success: true,
        data: {
          exists: false,
          path: workspaceDir
        }
      });
    }

  } catch (error: any) {
    console.error('❌ Error in checkWorkspaceExists:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to check workspace'
    });
  }
};

/**
 * Create workspace directory
 */
export const createWorkspaceDirectory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.body;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        error: 'interviewUuid is required'
      });
    }

    const workspaceDir = path.join(WORKSPACE_BASE_PATH, 'workspace', interviewUuid);
    
    await fs.mkdir(workspaceDir, { recursive: true });

    res.json({
      success: true,
      message: 'Workspace directory created',
      data: {
        path: workspaceDir
      }
    });

  } catch (error: any) {
    console.error('❌ Error in createWorkspaceDirectory:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create workspace directory'
    });
  }
};

/**
 * Recursively load files from directory
 */
async function loadFilesRecursively(
  dirPath: string, 
  basePath: string
): Promise<{ [path: string]: string }> {
  const files: { [path: string]: string } = {};

  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      const relativePath = path.relative(basePath, fullPath);

      if (entry.isDirectory()) {
        // Recursively load subdirectory
        const subFiles = await loadFilesRecursively(fullPath, basePath);
        Object.assign(files, subFiles);
      } else if (entry.isFile()) {
        // Load file content
        try {
          const content = await fs.readFile(fullPath, 'utf8');
          files[relativePath] = content;
        } catch (error) {
          console.warn(`⚠️ Could not read file ${relativePath}:`, error);
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dirPath}:`, error);
  }

  return files;
}

/**
 * Validate file path to prevent directory traversal attacks
 */
function validateFilePath(filePath: string): boolean {
  // Prevent directory traversal
  if (filePath.includes('..') || filePath.startsWith('/')) {
    return false;
  }
  
  // Only allow reasonable file extensions
  const allowedExtensions = [
    '.js', '.jsx', '.ts', '.tsx', '.json', '.html', '.css', '.scss',
    '.py', '.java', '.cpp', '.c', '.php', '.rb', '.go', '.rs',
    '.sql', '.xml', '.yaml', '.yml', '.md', '.txt', '.env'
  ];
  
  const extension = filePath.substring(filePath.lastIndexOf('.'));
  return allowedExtensions.includes(extension) || !extension;
}

/**
 * Sanitize file content to prevent malicious code injection
 */
function sanitizeContent(content: string): string {
  // Basic sanitization - remove null bytes and control characters
  return content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
}
